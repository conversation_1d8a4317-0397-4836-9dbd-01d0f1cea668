'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import {
  Search,
  Loader2,
  Check
} from 'lucide-react';
import { ComposioApp } from '@/types/composio';
import { createClient } from '@/lib/supabase/client';
import { MCPServerCard } from './mcp-server-card';
import { ComposioMCPService } from '@/lib/composio-api';
import { useQueryClient } from '@tanstack/react-query';
import { agentKeys } from '@/hooks/react-query/agents/keys';

interface MCPServerCarouselProps {
  className?: string;
}

export function MCPServerCarousel({ className }: MCPServerCarouselProps) {
  const queryClient = useQueryClient();
  const [apps, setApps] = useState<ComposioApp[]>([]);
  const [filteredApps, setFilteredApps] = useState<ComposioApp[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());
  const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set());
  const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set());

  // Helper function to get authenticated headers
  const getAuthHeaders = async () => {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('No authentication token available. Please sign in.');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    };
  };

  // Load supported apps using v3 API
  useEffect(() => {
    const loadApps = async () => {
      try {
        setLoading(true);

        const data = await ComposioMCPService.getSupportedApps();

        if (data.success) {
          setApps(data.apps);
          setFilteredApps(data.apps);
        } else {
          throw new Error('Failed to load supported integrations');
        }
      } catch (error) {
        console.error('Error loading apps:', error);
        toast.error("Failed to load MCP servers", {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      } finally {
        setLoading(false);
      }
    };

    loadApps();
  }, []);

  // Optimistic polling for faster connection detection
  const startOptimisticPolling = (appKey: string, appName: string) => {
    let attempts = 0;
    const maxAttempts = 20; // 2 minutes max

    const pollStatus = async () => {
      attempts++;

      try {
        const connectionRequestId = localStorage.getItem('composio_connection_request_id');
        if (!connectionRequestId) return; // User cancelled or cleared

        const statusResult = await ComposioMCPService.checkConnectionStatus(
          connectionRequestId,
          appKey as any
        );

        if (statusResult.success && statusResult.is_connected) {
          // Success! Clear storage and update UI
          localStorage.removeItem('composio_recently_connected');
          localStorage.removeItem('composio_connection_request_id');
          localStorage.removeItem('composio_connection_app_name');

          // Update connected apps immediately (optimistic update)
          setConnectedApps(prev => new Set(prev).add(appKey));

          // Reload connections and invalidate cache
          const connections = await ComposioMCPService.listUserConnections();
          const connectedAppKeys: Set<string> = new Set(
            connections.map((conn: any) => conn.app_key as string)
          );
          setConnectedApps(connectedAppKeys);

          queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
          queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

          toast.success("Authentication complete");

          return; // Stop polling
        }

        // Continue polling if not connected yet and under max attempts
        if (attempts < maxAttempts) {
          // Exponential backoff: start fast, slow down over time
          const delay = Math.min(1000 + attempts * 500, 6000);
          setTimeout(pollStatus, delay);
        } else {
          // Max attempts reached, clear storage silently
          localStorage.removeItem('composio_recently_connected');
          localStorage.removeItem('composio_connection_request_id');
          localStorage.removeItem('composio_connection_app_name');
        }

      } catch (error) {
        console.error('Error during optimistic polling:', error);

        if (attempts < maxAttempts) {
          // Retry with longer delay on error
          setTimeout(pollStatus, 3000);
        }
      }
    };

    // Start polling immediately, then after short delay
    setTimeout(pollStatus, 500);
  };

  // Load existing connections and handle post-OAuth refresh
  useEffect(() => {
    const loadConnections = async () => {
      try {
        const connections = await ComposioMCPService.listUserConnections();
        const connectedAppKeys: Set<string> = new Set(
          connections.map((conn: any) => conn.app_key as string)
        );
        setConnectedApps(connectedAppKeys);
      } catch (error) {
        console.error('Error loading connections:', error);
      }
    };

    // Check if user just returned from OAuth authentication
    const handlePostOAuthRefresh = async () => {
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      const connectionRequestId = localStorage.getItem('composio_connection_request_id');

      if (recentlyConnectedKey && connectionRequestId) {
        console.log(`Checking if ${recentlyConnectedKey} connection completed...`);

        try {
          // Check connection status immediately (no delay)
          const statusResult = await ComposioMCPService.checkConnectionStatus(
            connectionRequestId,
            recentlyConnectedKey as any
          );

          if (statusResult.success && statusResult.is_connected) {
            // Connection is established, clear flags and reload connections
            localStorage.removeItem('composio_recently_connected');
            localStorage.removeItem('composio_connection_request_id');
            localStorage.removeItem('composio_connection_app_name');

            // Reload connections to show updated state
            await loadConnections();

            // Invalidate React Query cache to refresh cursor agent selector
            queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
            queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

            toast.success("Authentication complete");
          } else {
            // Not connected yet, start optimistic polling
            const appName = localStorage.getItem('composio_connection_app_name') || recentlyConnectedKey;
            startOptimisticPolling(recentlyConnectedKey, appName);
          }
        } catch (error) {
          console.error('Error checking post-OAuth status:', error);
          // Start optimistic polling as fallback
          const appName = localStorage.getItem('composio_connection_app_name') || recentlyConnectedKey;
          startOptimisticPolling(recentlyConnectedKey, appName);
        }
      }
    };

    // Handle window focus for post-OAuth refresh
    const handleWindowFocus = () => {
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      if (recentlyConnectedKey) {
        handlePostOAuthRefresh();
      }
    };

    // Load connections immediately
    loadConnections();

    // Handle post-OAuth refresh
    handlePostOAuthRefresh();

    // Listen for window focus events to handle post-OAuth flow
    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [queryClient]);

  // Handle search functionality and sort by connection status
  useEffect(() => {
    let appsToFilter = apps;

    // Filter by search query if present
    if (searchQuery.trim()) {
      appsToFilter = apps.filter(app =>
      app.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    }

    // Sort so connected apps appear first
    const sortedApps = appsToFilter.sort((a, b) => {
      const aConnected = connectedApps.has(a.key);
      const bConnected = connectedApps.has(b.key);

      // Connected apps first
      if (aConnected && !bConnected) return -1;
      if (!aConnected && bConnected) return 1;

      // If both have same connection status, maintain original order
      return 0;
    });

    setFilteredApps(sortedApps);
  }, [searchQuery, apps, connectedApps]);



  // Handle MCP server connection - simplified v3 flow
  async function handleConnect(appKey: string, appName: string) {
    if (connectingApps.has(appKey) || connectedApps.has(appKey)) return;

    // Show instant connecting state
    setConnectingApps(prev => new Set(prev).add(appKey));

    // Show simple authenticating toast
    toast("Authenticating...");

    try {
      // Step 1: Initiate connection and get redirect URL immediately
      const initResult = await ComposioMCPService.initiateConnection(appKey);

      // Step 2: Store flags for post-OAuth handling
      localStorage.setItem('composio_recently_connected', appKey);
      localStorage.setItem('composio_connection_request_id', initResult.connection_request_id);
      localStorage.setItem('composio_connection_app_name', appName);

      // Step 3: Redirect immediately
      window.open(initResult.redirect_url, '_blank');

      // Start optimistic polling
      startOptimisticPolling(appKey, appName);

    } catch (error: any) {
      console.error('Connection error:', error);
      // Silently fail - no error toasts
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }

  // Handle viewing tools for connected MCP servers - simplified
  async function handleViewTools(appKey: string, appName: string) {
    try {
      const connection = await ComposioMCPService.getConnectionStatus(appKey);
      // No toasts - just check silently
    } catch (error: any) {
      console.error('Error viewing connection:', error);
      // No error toasts
    }
  }

  // Handle MCP server disconnection - simplified
  async function handleDisconnect(appKey: string, appName: string) {
    if (disconnectingApps.has(appKey) || !connectedApps.has(appKey)) return;

    setDisconnectingApps(prev => new Set(prev).add(appKey));

    try {
      // Optimistic update - remove from UI immediately
      setConnectedApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });

      // Call the v3 delete endpoint to remove the connection
      const success = await ComposioMCPService.deleteConnection(appKey);

      if (success) {
        // Invalidate React Query cache to refresh cursor agent selector
        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
        queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });
      } else {
        // Rollback optimistic update on failure
        setConnectedApps(prev => new Set(prev).add(appKey));
      }

    } catch (error: any) {
      console.error('Disconnect error:', error);
      // Rollback optimistic update on error
      setConnectedApps(prev => new Set(prev).add(appKey));
    } finally {
      setDisconnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }

  if (loading) {
    return (
      <div className={className}>
        <div className="flex justify-end items-center mb-2">
          <Skeleton className="h-8 w-16" />
        </div>
        <div className="flex gap-4 overflow-x-auto">
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className="h-14 w-44 rounded-lg flex-shrink-0" />
          ))}
        </div>
      </div>
    );
  }

  // Don't render if no apps are available
  if (apps.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      {/* Header with search input */}
      <div className="flex justify-between items-center mb-4">
        <span className="text-sm text-muted-foreground">
          connect the apps you need
        </span>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-8 pl-10 pr-4 text-sm w-48 bg-background focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        </div>
      </div>

      {/* Cards container */}
      <div className="flex gap-4 overflow-x-auto scrollbar-hide">
        {filteredApps.map((app) => (
          <MCPServerCard
            key={app.key}
            app={app}
            isConnected={connectedApps.has(app.key)}
            isConnecting={connectingApps.has(app.key)}
            onConnect={() => handleConnect(app.key, app.name)}
            onDisconnect={() => handleDisconnect(app.key, app.name)}
            onViewTools={() => handleViewTools(app.key, app.name)}
            isLoadingTools={false} // Simplified for v3
          />
        ))}
      </div>

      {/* Results info */}
      {searchQuery && (
        <div className="text-xs text-muted-foreground mt-2">
          {filteredApps.length} results for "{searchQuery}"
        </div>
      )}
    </div>
  );
}
